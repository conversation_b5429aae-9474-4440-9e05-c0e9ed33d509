import pymongo
import asyncio
import logging
from typing import Optional, List, Dict, Any, Tuple
from pymongo import MongoClient
from datetime import datetime, timedelta, timezone
from bson import ObjectId

logger = logging.getLogger(__name__)

class DatabaseManager:
    def __init__(self, mongo_url: str):
        self.mongo_url = mongo_url
        self.client = None
        self.db = None
        
    def connect(self):
        """Connect to MongoDB"""
        try:
            self.client = MongoClient(self.mongo_url)
            self.db = self.client['leakin']  # Use the leakin database
            # Test connection
            self.client.admin.command('ping')
            logger.info("Successfully connected to MongoDB")
        except Exception as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            raise e
    
    def disconnect(self):
        """Disconnect from MongoDB"""
        if self.client:
            self.client.close()
            logger.info("Disconnected from MongoDB")
    
    # License Key Operations
    def add_license_keys(self, keys: List[str]) -> int:
        """Add multiple license keys to the database"""
        collection = self.db['leakin-license-keys']
        
        key_docs = []
        for key in keys:
            # Check if key already exists
            if not collection.find_one({"key": key}):
                key_docs.append({
                    "key": key,
                    "redeemed": False,
                    "redeemed_by": None,
                    "redeemed_at": None,
                    "server_id": None,
                    "created_at": datetime.utcnow()
                })
        
        if key_docs:
            result = collection.insert_many(key_docs)
            logger.info(f"Added {len(result.inserted_ids)} new license keys")
            return len(result.inserted_ids)
        return 0
    
    def redeem_license_key(self, key: str, user_id: int, server_id: int) -> bool:
        """Redeem a license key for a user and server"""
        collection = self.db['leakin-license-keys']
        
        # Find unredeemed key
        key_doc = collection.find_one({"key": key, "redeemed": False})
        if not key_doc:
            return False
        
        # Update key as redeemed
        result = collection.update_one(
            {"_id": key_doc["_id"]},
            {
                "$set": {
                    "redeemed": True,
                    "redeemed_by": user_id,
                    "redeemed_at": datetime.utcnow(),
                    "server_id": server_id
                }
            }
        )
        
        return result.modified_count > 0
    
    def get_user_license_keys(self, user_id: int) -> List[Dict[str, Any]]:
        """Get all license keys owned by a user"""
        collection = self.db['leakin-license-keys']
        return list(collection.find({"redeemed_by": user_id}))
    
    def get_server_license_key(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get the active license key for a server (only if not disabled)"""
        collection = self.db['leakin-license-keys']
        return collection.find_one({
            "server_id": server_id,
            "redeemed": True,
            "$or": [
                {"disabled": {"$exists": False}},
                {"disabled": False}
            ]
        })
    
    def transfer_key_to_server(self, key: str, user_id: int, new_server_id: int) -> bool:
        """Transfer a license key to a different server with cleanup of old server's data"""
        collection = self.db['leakin-license-keys']
        
        # First get the current server_id to clean up
        current_key = collection.find_one({"key": key, "redeemed_by": user_id})
        if not current_key:
            return False
            
        old_server_id = current_key.get('server_id')
        
        # Update the server_id
        result = collection.update_one(
            {"key": key, "redeemed_by": user_id},
            {"$set": {"server_id": new_server_id}}
        )
        
        # If transfer was successful, clean up old server's role assignments
        if result.modified_count > 0 and old_server_id:
            # Clean up server config for old server
            config_collection = self.db['leakin-server-configs']
            config_collection.delete_one({"server_id": old_server_id})
            logger.info(f"Transferred key {key} from server {old_server_id} to {new_server_id}, cleaned up old config")
            
        return result.modified_count > 0
    
    def transfer_key_to_user(self, key: str, current_user_id: int, new_user_id: int) -> bool:
        """Transfer ownership of a license key to another user with cleanup"""
        collection = self.db['leakin-license-keys']
        
        # First get the server_id to clean up
        current_key = collection.find_one({"key": key, "redeemed_by": current_user_id})
        if not current_key:
            return False
            
        server_id = current_key.get('server_id')
        
        # Update the owner
        result = collection.update_one(
            {"key": key, "redeemed_by": current_user_id},
            {"$set": {"redeemed_by": new_user_id}}
        )
        
        # If transfer was successful, clean up server configuration
        if result.modified_count > 0 and server_id:
            # Clean up server config since the new owner will need to reconfigure
            config_collection = self.db['leakin-server-configs']
            config_collection.delete_one({"server_id": server_id})
            logger.info(f"Transferred key {key} from user {current_user_id} to {new_user_id} for server {server_id}, cleaned up server config")
            
        return result.modified_count > 0
    
    # Server Configuration Operations
    def save_server_config(self, server_id: int, config: Dict[str, Any]) -> bool:
        """Save server configuration"""
        collection = self.db['leakin-server-configs']
        
        result = collection.update_one(
            {"server_id": server_id},
            {"$set": {**config, "updated_at": datetime.utcnow()}},
            upsert=True
        )
        
        return result.upserted_id is not None or result.modified_count > 0
    
    def get_server_config(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get server configuration"""
        collection = self.db['leakin-server-configs']
        return collection.find_one({"server_id": server_id})
    
    def update_server_config_field(self, server_id: int, field: str, value: Any) -> bool:
        """Update a specific field in server configuration"""
        collection = self.db['leakin-server-configs']
        
        result = collection.update_one(
            {"server_id": server_id},
            {"$set": {field: value, "updated_at": datetime.utcnow()}},
            upsert=True
        )
        
        return result.upserted_id is not None or result.modified_count > 0
    
    def add_ignored_user(self, server_id: int, user_id: int) -> bool:
        """Add a user to the ignored list for a server"""
        collection = self.db['leakin-server-configs']
        
        result = collection.update_one(
            {"server_id": server_id},
            {"$addToSet": {"ignored_users": user_id}},
            upsert=True
        )
        
        return result.upserted_id is not None or result.modified_count > 0
    
    def remove_ignored_user(self, server_id: int, user_id: int) -> bool:
        """Remove a user from the ignored list for a server"""
        collection = self.db['leakin-server-configs']
        
        result = collection.update_one(
            {"server_id": server_id},
            {"$pull": {"ignored_users": user_id}}
        )
        
        return result.modified_count > 0
    
    def is_server_licensed(self, server_id: int) -> bool:
        """Check if a server has a valid license"""
        license_key = self.get_server_license_key(server_id)
        return license_key is not None and not license_key.get('disabled', False)

    def disable_license_key(self, key: str) -> bool:
        """Disable a license key and all its features"""
        collection = self.db['leakin-license-keys']

        result = collection.update_one(
            {"key": key},
            {"$set": {"disabled": True, "disabled_at": datetime.utcnow()}}
        )

        return result.modified_count > 0

    def enable_license_key(self, key: str) -> bool:
        """Enable a license key"""
        collection = self.db['leakin-license-keys']

        result = collection.update_one(
            {"key": key},
            {"$set": {"disabled": False}, "$unset": {"disabled_at": ""}}
        )

        return result.modified_count > 0

    def get_license_key_by_key(self, key: str) -> Optional[Dict[str, Any]]:
        """Get license key information by key string"""
        collection = self.db['leakin-license-keys']
        return collection.find_one({"key": key})

    def get_server_license_key_full(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get the full license key information for a server"""
        collection = self.db['leakin-license-keys']
        return collection.find_one({"server_id": server_id, "redeemed": True})
    
    def is_server_configured(self, server_id: int) -> tuple[bool, List[str]]:
        """Check if server is properly configured"""
        config = self.get_server_config(server_id)
        if not config:
            return False, ["No configuration found"]
        
        required_fields = ['role_id', 'channel_id', 'trigger_word']
        missing_fields = []
        
        for field in required_fields:
            if field not in config or config[field] is None:
                missing_fields.append(field)
        
        return len(missing_fields) == 0, missing_fields
        
    # Gender Verification Methods
    def set_gender_verification_settings(self, server_id: int, channel_id: int, category_id: int, 
                                       support_role_id: int, paper_text: str) -> bool:
        """Save gender verification settings for a server"""
        collection = self.db['leakin-gender-verification']
        
        result = collection.update_one(
            {"server_id": server_id},
            {"$set": {
                "channel_id": channel_id,
                "category_id": category_id,
                "support_role_id": support_role_id,
                "paper_text": paper_text,
                "updated_at": datetime.utcnow()
            }},
            upsert=True
        )
        
        return result.upserted_id is not None or result.modified_count > 0
    
    def get_gender_verification_settings(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get gender verification settings for a server"""
        collection = self.db['leakin-gender-verification']
        return collection.find_one({"server_id": server_id})
    
    def create_gender_verification_ticket(self, server_id: int, user_id: int) -> str:
        """Create a new gender verification ticket"""
        collection = self.db['leakin-gender-tickets']
        
        # Check if user already has an open ticket
        existing_ticket = collection.find_one({
            "server_id": server_id,
            "user_id": user_id,
            "status": "open"
        })
        
        if existing_ticket:
            return "existing"
            
        # Check if user had a ticket closed in the last 12 hours
        recent_ticket = collection.find_one({
            "server_id": server_id,
            "user_id": user_id,
            "status": "closed",
            "closed_at": {"$gt": datetime.utcnow() - timedelta(hours=12)}
        })
        
        if recent_ticket:
            return "recent"
            
        # Create new ticket
        ticket = {
            "server_id": server_id,
            "user_id": user_id,
            "channel_id": None,
            "status": "open",
            "created_at": datetime.utcnow(),
            "closed_at": None
        }
        
        result = collection.insert_one(ticket)
        return str(result.inserted_id)
    
    def close_gender_verification_ticket(self, ticket_id: str, channel_id: int) -> bool:
        """Close a gender verification ticket"""
        collection = self.db['leakin-gender-tickets']
        
        result = collection.update_one(
            {"_id": ObjectId(ticket_id)},
            {"$set": {
                "status": "closed",
                "closed_at": datetime.utcnow(),
                "channel_id": channel_id
            }}
        )
        
        return result.modified_count > 0
    
    def get_user_open_ticket(self, server_id: int, user_id: int) -> Optional[Dict[str, Any]]:
        """Get user's open gender verification ticket"""
        collection = self.db['leakin-gender-tickets']
        return collection.find_one({
            "server_id": server_id,
            "user_id": user_id,
            "status": "open"
        })
    
    def cleanup_old_tickets(self):
        """Clean up tickets that have been open for more than 24 hours"""
        try:
            # Check if database is initialized by trying to access a collection
            try:
                collection = self.db.get_collection('leakin-gender-tickets')
            except Exception as e:
                logger.error(f"Database error: {e}")
                return 0

            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=24)

            result = collection.update_many(
                {
                    "status": "open",
                    "created_at": {"$lt": cutoff_time}
                },
                {"$set": {"status": "closed", "closed_at": datetime.now(timezone.utc)}}
            )

            logger.info(f"Cleaned up {result.modified_count} old tickets")
            return result.modified_count

        except Exception as e:
            logger.error(f"Error in cleanup_old_tickets: {e}", exc_info=True)
            return 0

    # TempVoice Methods
    def set_tempvoice_settings(self, server_id: int, interface_channel_id: int, creator_channel_id: int, default_user_limit: Optional[int] = None) -> bool:
        """Save TempVoice settings for a server"""
        collection = self.db['leakin-tempvoice-settings']

        result = collection.update_one(
            {"server_id": server_id},
            {"$set": {
                "interface_channel_id": interface_channel_id,
                "creator_channel_id": creator_channel_id,
                "default_user_limit": default_user_limit,
                "updated_at": datetime.utcnow()
            }},
            upsert=True
        )

        return result.upserted_id is not None or result.modified_count > 0

    def get_tempvoice_settings(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get TempVoice settings for a server"""
        collection = self.db['leakin-tempvoice-settings']
        return collection.find_one({"server_id": server_id})

    def create_temp_channel(self, server_id: int, user_id: int, channel_id: int) -> bool:
        """Create a temporary voice channel record"""
        collection = self.db['leakin-temp-channels']

        # Check if user already has a channel
        existing = collection.find_one({
            "server_id": server_id,
            "owner_id": user_id,
            "active": True
        })

        if existing:
            return False

        channel_doc = {
            "server_id": server_id,
            "channel_id": channel_id,
            "owner_id": user_id,
            "active": True,
            "user_limit": None,
            "blocked_users": [],
            "locked": False,
            "created_at": datetime.utcnow()
        }

        result = collection.insert_one(channel_doc)
        return result.inserted_id is not None

    def get_temp_channel(self, channel_id: int) -> Optional[Dict[str, Any]]:
        """Get temporary channel data"""
        collection = self.db['leakin-temp-channels']
        return collection.find_one({"channel_id": channel_id, "active": True})

    def get_user_temp_channel(self, server_id: int, user_id: int) -> Optional[Dict[str, Any]]:
        """Get user's active temporary channel"""
        collection = self.db['leakin-temp-channels']
        return collection.find_one({
            "server_id": server_id,
            "owner_id": user_id,
            "active": True
        })

    def delete_temp_channel(self, channel_id: int) -> bool:
        """Mark temporary channel as inactive"""
        collection = self.db['leakin-temp-channels']

        result = collection.update_one(
            {"channel_id": channel_id},
            {"$set": {"active": False, "deleted_at": datetime.utcnow()}}
        )

        return result.modified_count > 0

    def update_temp_channel_owner(self, channel_id: int, new_owner_id: int) -> bool:
        """Transfer ownership of a temporary channel"""
        collection = self.db['leakin-temp-channels']

        result = collection.update_one(
            {"channel_id": channel_id, "active": True},
            {"$set": {"owner_id": new_owner_id}}
        )

        return result.modified_count > 0

    def set_temp_channel_limit(self, channel_id: int, user_limit: Optional[int]) -> bool:
        """Set user limit for temporary channel"""
        collection = self.db['leakin-temp-channels']

        result = collection.update_one(
            {"channel_id": channel_id, "active": True},
            {"$set": {"user_limit": user_limit}}
        )

        return result.modified_count > 0

    def block_user_from_temp_channel(self, channel_id: int, user_id: int) -> bool:
        """Block a user from a temporary channel"""
        collection = self.db['leakin-temp-channels']

        result = collection.update_one(
            {"channel_id": channel_id, "active": True},
            {"$addToSet": {"blocked_users": user_id}}
        )

        return result.modified_count > 0

    def unblock_user_from_temp_channel(self, channel_id: int, user_id: int) -> bool:
        """Unblock a user from a temporary channel"""
        collection = self.db['leakin-temp-channels']

        result = collection.update_one(
            {"channel_id": channel_id, "active": True},
            {"$pull": {"blocked_users": user_id}}
        )

        return result.modified_count > 0

    def set_temp_channel_lock(self, channel_id: int, locked: bool) -> bool:
        """Lock or unlock a temporary channel"""
        collection = self.db['leakin-temp-channels']

        result = collection.update_one(
            {"channel_id": channel_id, "active": True},
            {"$set": {"locked": locked}}
        )

        return result.modified_count > 0

    def get_all_active_temp_channels(self) -> List[Dict[str, Any]]:
        """Get all active temporary channels across all servers"""
        collection = self.db['leakin-temp-channels']
        return list(collection.find({"active": True}))

    # Vent System Methods
    def set_vent_settings(self, server_id: int, vent_channel_id: int) -> bool:
        """Save vent system settings for a server"""
        collection = self.db['leakin-vent-settings']

        result = collection.update_one(
            {"server_id": server_id},
            {"$set": {
                "vent_channel_id": vent_channel_id,
                "updated_at": datetime.utcnow()
            }},
            upsert=True
        )

        return result.upserted_id is not None or result.modified_count > 0

    def get_vent_settings(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get vent system settings for a server"""
        collection = self.db['leakin-vent-settings']
        return collection.find_one({"server_id": server_id})

    def log_vent_message(self, server_id: int, user_id: int, username: str, message: str) -> bool:
        """Log a vent message for moderation purposes"""
        collection = self.db['leakin-vent-logs']

        log_doc = {
            "server_id": server_id,
            "user_id": user_id,
            "username": username,
            "message": message,
            "timestamp": datetime.utcnow()
        }

        result = collection.insert_one(log_doc)
        return result.inserted_id is not None

    # Sticky Message Methods
    def set_sticky_message(self, server_id: int, channel_id: int, content: str, created_by: int) -> bool:
        """Set or update a sticky message for a channel"""
        collection = self.db['leakin-sticky-messages']

        result = collection.update_one(
            {"server_id": server_id, "channel_id": channel_id},
            {"$set": {
                "content": content,
                "created_by": created_by,
                "message_id": None,  # Will be set when message is posted
                "updated_at": datetime.now(timezone.utc)
            }},
            upsert=True
        )

        return result.upserted_id is not None or result.modified_count > 0

    def get_sticky_message(self, server_id: int, channel_id: int) -> Optional[Dict[str, Any]]:
        """Get sticky message settings for a specific channel"""
        collection = self.db['leakin-sticky-messages']
        return collection.find_one({"server_id": server_id, "channel_id": channel_id})

    def get_all_sticky_messages(self, server_id: int) -> List[Dict[str, Any]]:
        """Get all sticky messages for a server"""
        collection = self.db['leakin-sticky-messages']
        return list(collection.find({"server_id": server_id}))

    def update_sticky_message_id(self, server_id: int, channel_id: int, message_id: int) -> bool:
        """Update the message ID for a sticky message"""
        collection = self.db['leakin-sticky-messages']

        result = collection.update_one(
            {"server_id": server_id, "channel_id": channel_id},
            {"$set": {"message_id": message_id}}
        )

        return result.modified_count > 0

    def remove_sticky_message(self, server_id: int, channel_id: int) -> bool:
        """Remove a sticky message from a channel"""
        collection = self.db['leakin-sticky-messages']

        result = collection.delete_one({"server_id": server_id, "channel_id": channel_id})
        return result.deleted_count > 0

    def log_sticky_activity(self, server_id: int, channel_id: int, user_id: int, username: str, action: str, content: str = None) -> bool:
        """Log sticky message activity for moderation purposes"""
        collection = self.db['leakin-sticky-logs']

        log_doc = {
            "server_id": server_id,
            "channel_id": channel_id,
            "user_id": user_id,
            "username": username,
            "action": action,  # 'created', 'removed', 'reposted'
            "content": content,
            "timestamp": datetime.now(timezone.utc)
        }

        result = collection.insert_one(log_doc)
        return result.inserted_id is not None

    # Comprehensive Logging System
    def log_bot_activity(self, server_id: int, user_id: int, username: str, action: str,
                        details: str = None, category: str = "general", channel_id: int = None) -> bool:
        """Log all bot activity for dashboard viewing (max 100 logs per server)"""
        collection = self.db['leakin-bot-logs']

        log_doc = {
            "server_id": server_id,
            "user_id": user_id,
            "username": username,
            "action": action,
            "details": details,
            "category": category,  # 'repping', 'vent', 'tempvoice', 'sticky', 'dm_support', 'gender_verification', 'config', 'general'
            "channel_id": channel_id,
            "timestamp": datetime.now(timezone.utc)
        }

        # Insert the new log
        result = collection.insert_one(log_doc)

        if result.inserted_id:
            # Clean up old logs - keep only the most recent 100 logs per server
            self._cleanup_old_logs(server_id)
            return True

        return False

    def _cleanup_old_logs(self, server_id: int, max_logs: int = 100):
        """Clean up old logs, keeping only the most recent max_logs entries per server"""
        collection = self.db['leakin-bot-logs']

        try:
            # Count current logs for this server
            total_logs = collection.count_documents({"server_id": server_id})

            if total_logs > max_logs:
                # Find the oldest logs to delete
                logs_to_delete = total_logs - max_logs

                # Get the oldest logs
                oldest_logs = list(collection.find(
                    {"server_id": server_id}
                ).sort("timestamp", 1).limit(logs_to_delete))

                if oldest_logs:
                    # Delete the oldest logs
                    oldest_ids = [log["_id"] for log in oldest_logs]
                    collection.delete_many({"_id": {"$in": oldest_ids}})

                    logger.info(f"Cleaned up {len(oldest_ids)} old log entries for server {server_id}")

        except Exception as e:
            logger.error(f"Error cleaning up old logs for server {server_id}: {e}")

    def get_bot_logs(self, server_id: int, limit: int = 100, category: str = None,
                    start_date: datetime = None, end_date: datetime = None) -> List[Dict[str, Any]]:
        """Get bot activity logs for dashboard"""
        collection = self.db['leakin-bot-logs']

        query = {"server_id": server_id}

        if category:
            query["category"] = category

        if start_date or end_date:
            date_query = {}
            if start_date:
                date_query["$gte"] = start_date
            if end_date:
                date_query["$lte"] = end_date
            query["timestamp"] = date_query

        return list(collection.find(query).sort("timestamp", -1).limit(limit))

    def get_log_statistics(self, server_id: int, days: int = 7) -> Dict[str, Any]:
        """Get log statistics for dashboard"""
        collection = self.db['leakin-bot-logs']
        now = datetime.now(timezone.utc)

        # Handle "all time" stats (when days is 0 or very large)
        if days == 0 or days > 36500:  # 0 or more than 100 years = all time
            # For all time, we don't filter by date at all
            date_match = {"$match": {"server_id": server_id}}
            period_description = "all time"
            total_logs = collection.count_documents({"server_id": server_id})
        else:
            # For specific time period, filter by date
            start_date = now - timedelta(days=days)
            date_match = {
                "$match": {
                    "server_id": server_id,
                    "timestamp": {"$gte": start_date}
                }
            }
            period_description = f"last {days} days"
            total_logs = collection.count_documents({
                "server_id": server_id,
                "timestamp": {"$gte": start_date}
            })

        # Logs by category
        pipeline = [
            date_match,
            {"$group": {"_id": "$category", "count": {"$sum": 1}}},
            {"$sort": {"count": -1}}
        ]

        category_stats = list(collection.aggregate(pipeline))

        return {
            "total_logs": total_logs,
            "category_stats": category_stats,
            "period_days": days if days > 0 and days <= 36500 else 0,
            "period_description": period_description
        }

    def get_server_statistics(self, server_id: int) -> Dict[str, Any]:
        """Get comprehensive server statistics for dashboard"""
        stats = {}

        # Initialize collections that will be used throughout the method
        logs_collection = self.db['leakin-bot-logs']

        # Repping statistics
        config_collection = self.db['leakin-server-config']
        config = config_collection.find_one({"server_id": server_id})

        if config and config.get('role_id'):
            # Count users who currently have repping role assignments (live count)
            # Get users who have had role assignments more recently than removals
            pipeline = [
                {"$match": {
                    "server_id": server_id,
                    "category": "repping",
                    "action": {"$regex": "^Role (assigned|removed):"}
                }},
                {"$sort": {"timestamp": -1}},
                {"$group": {
                    "_id": "$user_id",
                    "latest_action": {"$first": "$action"}
                }},
                {"$match": {"latest_action": {"$regex": "^Role assigned:"}}},
                {"$count": "active_reppers"}
            ]

            result = list(logs_collection.aggregate(pipeline))
            stats['repping_users'] = result[0]['active_reppers'] if result else 0
        else:
            stats['repping_users'] = 0

        # Temp voice statistics
        temp_channels_collection = self.db['leakin-temp-channels']
        active_temp_channels = temp_channels_collection.count_documents({
            "server_id": server_id,
            "active": True
        })
        stats['active_temp_channels'] = active_temp_channels

        # Total temp channels created (last 30 days)
        total_temp_channels = temp_channels_collection.count_documents({
            "server_id": server_id,
            "created_at": {"$gte": datetime.now(timezone.utc) - timedelta(days=30)}
        })
        stats['total_temp_channels_30d'] = total_temp_channels

        # Sticky messages (live count)
        sticky_collection = self.db['leakin-sticky-messages']
        active_sticky_messages = sticky_collection.count_documents({
            "server_id": server_id
        })
        stats['active_sticky_messages'] = active_sticky_messages

        # Gender verification tickets (last 30 days)
        gender_tickets_collection = self.db['leakin-gender-tickets']
        gender_tickets = gender_tickets_collection.count_documents({
            "server_id": server_id,
            "created_at": {"$gte": datetime.now(timezone.utc) - timedelta(days=30)}
        })
        stats['gender_tickets_30d'] = gender_tickets

        # DM Support tickets (last 30 days)
        dm_support_collection = self.db['leakin-dm-support-tickets']
        dm_support_tickets = dm_support_collection.count_documents({
            "server_id": server_id,
            "created_at": {"$gte": datetime.now(timezone.utc) - timedelta(days=30)}
        })
        stats['dm_support_tickets_30d'] = dm_support_tickets

        # Bot activity (live/all time)
        total_activity = logs_collection.count_documents({
            "server_id": server_id
        })
        stats['total_bot_activity'] = total_activity

        return stats

    def cleanup_all_server_logs(self, server_id: int, max_logs: int = 100) -> int:
        """Manually clean up logs for a server and return number of deleted logs"""
        collection = self.db['leakin-bot-logs']

        try:
            # Count current logs
            total_logs = collection.count_documents({"server_id": server_id})

            if total_logs <= max_logs:
                return 0

            # Calculate how many to delete
            logs_to_delete = total_logs - max_logs

            # Get the oldest logs
            oldest_logs = list(collection.find(
                {"server_id": server_id}
            ).sort("timestamp", 1).limit(logs_to_delete))

            if oldest_logs:
                # Delete the oldest logs
                oldest_ids = [log["_id"] for log in oldest_logs]
                result = collection.delete_many({"_id": {"$in": oldest_ids}})

                logger.info(f"Manual cleanup: Deleted {result.deleted_count} old log entries for server {server_id}")
                return result.deleted_count

            return 0

        except Exception as e:
            logger.error(f"Error in manual log cleanup for server {server_id}: {e}")
            return 0

    # DM Support System Methods
    def set_dm_support_settings(self, server_id: int, category_id: int, support_role_id: int, logs_channel_id: int) -> bool:
        """Save DM support settings for a server"""
        collection = self.db['leakin-dm-support-settings']

        result = collection.update_one(
            {"server_id": server_id},
            {"$set": {
                "category_id": category_id,
                "support_role_id": support_role_id,
                "logs_channel_id": logs_channel_id,
                "enabled": True,
                "updated_at": datetime.now(timezone.utc)
            }},
            upsert=True
        )

        return result.upserted_id is not None or result.modified_count > 0

    def get_dm_support_settings(self, server_id: int) -> Optional[Dict[str, Any]]:
        """Get DM support settings for a server"""
        collection = self.db['leakin-dm-support-settings']
        return collection.find_one({"server_id": server_id, "enabled": True})

    def get_user_servers_with_dm_support(self, user_id: int) -> List[Dict[str, Any]]:
        """Get all servers where user is a member and DM support is enabled"""
        collection = self.db['leakin-dm-support-settings']
        return list(collection.find({"enabled": True}))

    def create_dm_support_ticket(self, server_id: int, user_id: int, initial_message: str) -> str:
        """Create a new DM support ticket"""
        collection = self.db['leakin-dm-support-tickets']

        # Check if user already has an open ticket for this server
        existing_ticket = collection.find_one({
            "server_id": server_id,
            "user_id": user_id,
            "status": "open"
        })

        if existing_ticket:
            return "existing"

        # Create new ticket
        ticket = {
            "server_id": server_id,
            "user_id": user_id,
            "channel_id": None,
            "status": "open",
            "initial_message": initial_message,
            "messages": [],
            "created_at": datetime.now(timezone.utc),
            "closed_at": None,
            "closed_by": None,
            "close_reason": None
        }

        result = collection.insert_one(ticket)
        return str(result.inserted_id)

    def get_user_open_dm_ticket(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get user's open DM support ticket (globally)"""
        collection = self.db['leakin-dm-support-tickets']
        return collection.find_one({
            "user_id": user_id,
            "status": "open"
        })

    def get_dm_ticket_by_channel(self, channel_id: int) -> Optional[Dict[str, Any]]:
        """Get DM support ticket by channel ID"""
        collection = self.db['leakin-dm-support-tickets']
        return collection.find_one({
            "channel_id": channel_id,
            "status": "open"
        })

    def update_dm_ticket_channel(self, ticket_id: str, channel_id: int) -> bool:
        """Update the channel ID for a DM support ticket"""
        collection = self.db['leakin-dm-support-tickets']

        result = collection.update_one(
            {"_id": ObjectId(ticket_id)},
            {"$set": {"channel_id": channel_id}}
        )

        return result.modified_count > 0

    def add_message_to_dm_ticket(self, ticket_id: str, user_id: int, username: str, message: str, is_staff: bool = False) -> bool:
        """Add a message to a DM support ticket"""
        collection = self.db['leakin-dm-support-tickets']

        message_doc = {
            "user_id": user_id,
            "username": username,
            "message": message,
            "is_staff": is_staff,
            "timestamp": datetime.now(timezone.utc)
        }

        result = collection.update_one(
            {"_id": ObjectId(ticket_id)},
            {"$push": {"messages": message_doc}}
        )

        return result.modified_count > 0

    def close_dm_support_ticket(self, ticket_id: str, closed_by: int, reason: str) -> bool:
        """Close a DM support ticket"""
        collection = self.db['leakin-dm-support-tickets']

        result = collection.update_one(
            {"_id": ObjectId(ticket_id)},
            {"$set": {
                "status": "closed",
                "closed_at": datetime.now(timezone.utc),
                "closed_by": closed_by,
                "close_reason": reason
            }}
        )

        return result.modified_count > 0

    def get_dm_ticket_transcript(self, ticket_id: str) -> Optional[Dict[str, Any]]:
        """Get full transcript of a DM support ticket"""
        collection = self.db['leakin-dm-support-tickets']
        return collection.find_one({"_id": ObjectId(ticket_id)})
