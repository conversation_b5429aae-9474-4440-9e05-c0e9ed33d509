{% extends "base.html" %}

{% block title %}Giveaways - {{ server_info.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-gift me-2"></i>Giveaways</h2>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createGiveawayModal">
                    <i class="fas fa-plus me-2"></i>Create Giveaway
                </button>
            </div>

            <!-- Server Info -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        {% if server_info.icon %}
                        <img src="{{ server_info.icon }}" alt="Server Icon" class="rounded-circle me-3" width="50" height="50">
                        {% else %}
                        <div class="bg-secondary rounded-circle me-3 d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                            <i class="fas fa-server text-white"></i>
                        </div>
                        {% endif %}
                        <div>
                            <h5 class="mb-1">{{ server_info.name }}</h5>
                            <small class="text-muted">{{ server_info.member_count }} members</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Giveaways List -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-list me-2"></i>Server Giveaways</h5>
                </div>
                <div class="card-body">
                    {% if giveaways %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Item</th>
                                        <th>Channel</th>
                                        <th>Host</th>
                                        <th>Winners</th>
                                        <th>Entries</th>
                                        <th>Status</th>
                                        <th>End Time</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for giveaway in giveaways %}
                                    <tr>
                                        <td>
                                            <strong>{{ giveaway.item }}</strong>
                                            <br>
                                            <small class="text-muted">{{ giveaway.requirements[:50] }}{% if giveaway.requirements|length > 50 %}...{% endif %}</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">#{{ giveaway.channel_id }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ giveaway.host_user_id }}</span>
                                        </td>
                                        <td>{{ giveaway.winners }}</td>
                                        <td>{{ giveaway.entries|length }}</td>
                                        <td>
                                            {% if giveaway.ended %}
                                                <span class="badge bg-danger">Ended</span>
                                            {% elif giveaway.end_time <= moment().utc() %}
                                                <span class="badge bg-warning">Expired</span>
                                            {% else %}
                                                <span class="badge bg-success">Active</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <small>{{ giveaway.end_time.strftime('%Y-%m-%d %H:%M UTC') }}</small>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-gift fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No giveaways found</h5>
                            <p class="text-muted">Create your first giveaway to get started!</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Giveaway Modal -->
<div class="modal fade" id="createGiveawayModal" tabindex="-1" aria-labelledby="createGiveawayModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createGiveawayModalLabel">
                    <i class="fas fa-gift me-2"></i>Create Giveaway
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="{{ url_for('create_giveaway') }}">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="channel_id" class="form-label">Channel</label>
                                <select class="form-select" id="channel_id" name="channel_id" required>
                                    <option value="">Select a channel...</option>
                                    {% for channel in channels %}
                                    <option value="{{ channel.id }}">#{{ channel.name }}</option>
                                    {% endfor %}
                                </select>
                                <div class="form-text">Channel where the giveaway will be posted</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="host_user_id" class="form-label">Host User ID</label>
                                <input type="number" class="form-control" id="host_user_id" name="host_user_id" required>
                                <div class="form-text">Discord user ID of the giveaway host</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="item" class="form-label">Prize/Item</label>
                        <input type="text" class="form-control" id="item" name="item" required maxlength="200">
                        <div class="form-text">What is being given away (will be the embed title)</div>
                    </div>

                    <div class="mb-3">
                        <label for="requirements" class="form-label">Requirements</label>
                        <textarea class="form-control" id="requirements" name="requirements" rows="3" required maxlength="1000"></textarea>
                        <div class="form-text">Entry requirements (will be the embed description)</div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="winners" class="form-label">Number of Winners</label>
                                <input type="number" class="form-control" id="winners" name="winners" min="1" max="50" value="1" required>
                                <div class="form-text">How many winners to select (1-50)</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Duration</label>
                        <div class="row">
                            <div class="col-md-4">
                                <label for="duration_days" class="form-label">Days</label>
                                <input type="number" class="form-control" id="duration_days" name="duration_days" min="0" max="30" value="0">
                            </div>
                            <div class="col-md-4">
                                <label for="duration_hours" class="form-label">Hours</label>
                                <input type="number" class="form-control" id="duration_hours" name="duration_hours" min="0" max="23" value="0">
                            </div>
                            <div class="col-md-4">
                                <label for="duration_minutes" class="form-label">Minutes</label>
                                <input type="number" class="form-control" id="duration_minutes" name="duration_minutes" min="1" max="59" value="60">
                            </div>
                        </div>
                        <div class="form-text">How long the giveaway should run (minimum 1 minute)</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-gift me-2"></i>Create Giveaway
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Form validation
document.getElementById('createGiveawayModal').addEventListener('show.bs.modal', function () {
    // Reset form when modal opens
    const form = this.querySelector('form');
    form.reset();
    document.getElementById('duration_minutes').value = '60';
});

// Validate duration
function validateDuration() {
    const days = parseInt(document.getElementById('duration_days').value) || 0;
    const hours = parseInt(document.getElementById('duration_hours').value) || 0;
    const minutes = parseInt(document.getElementById('duration_minutes').value) || 0;
    
    if (days === 0 && hours === 0 && minutes === 0) {
        alert('Duration must be at least 1 minute');
        return false;
    }
    
    return true;
}

// Add validation to form submit
document.querySelector('#createGiveawayModal form').addEventListener('submit', function(e) {
    if (!validateDuration()) {
        e.preventDefault();
    }
});
</script>
{% endblock %}
