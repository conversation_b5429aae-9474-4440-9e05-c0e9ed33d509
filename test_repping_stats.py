#!/usr/bin/env python3
"""
Test script to debug repping stats functionality
"""

from database import DatabaseManager

def test_repping_stats():
    # Use the same MongoDB URL as in main.py
    MONGO_URL = "mongodb+srv://rustymag:rrM46&<EMAIL>/leakin?retryWrites=true&w=majority&appName=Cluster0"
    
    db = DatabaseManager(MONGO_URL)
    db.connect()
    
    print("🔍 Testing repping stats functionality...")
    
    # Get all servers with repping logs
    logs_collection = db.db['leakin-bot-logs']
    
    # Find all servers with repping activity
    servers_with_repping = logs_collection.distinct("server_id", {"category": "repping"})
    print(f"📊 Found {len(servers_with_repping)} servers with repping activity")
    
    for server_id in servers_with_repping[:3]:  # Test first 3 servers
        print(f"\n🏠 Testing server {server_id}:")
        
        # Get all repping logs for this server
        repping_logs = list(logs_collection.find({
            "server_id": server_id,
            "category": "repping"
        }).sort("timestamp", -1).limit(10))
        
        print(f"  📝 Found {len(repping_logs)} recent repping logs:")
        for log in repping_logs[:5]:  # Show first 5
            print(f"    - {log['username']}: {log['action']} ({log['timestamp']})")
        
        # Test the new method
        current_repping_users = db.get_current_repping_users(server_id)
        print(f"  👥 Current repping users: {len(current_repping_users)}")
        for user in current_repping_users[:5]:  # Show first 5
            print(f"    - {user['username']} (assigned: {user['assigned_at']})")
        
        # Test server config
        config = db.get_server_config(server_id)
        print(f"  ⚙️ Server config: {config}")

        # Test server stats
        server_stats = db.get_server_statistics(server_id)
        print(f"  📈 Server stats repping count: {server_stats.get('repping_users', 0)}")
        
        # Test temp voice channels
        temp_channels = db.get_active_temp_channels_with_users(server_id)
        print(f"  🎤 Active temp channels: {len(temp_channels)}")
        for channel in temp_channels[:3]:  # Show first 3
            print(f"    - {channel['owner_username']} (created: {channel['created_at']})")
    
    db.disconnect()
    print("\n✅ Test completed!")

if __name__ == "__main__":
    test_repping_stats()
