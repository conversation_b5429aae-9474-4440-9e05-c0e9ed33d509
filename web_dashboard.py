from flask import Flask, render_template, request, redirect, url_for, session, flash, jsonify
from datetime import datetime, timezone, timedelta
import logging
from database import DatabaseManager
from typing import Optional, Dict, Any, List

# Configuration
MONGO_URL = "mongodb+srv://rustymag:rrM46&<EMAIL>/leakin?retryWrites=true&w=majority&appName=Cluster0"
SECRET_KEY = "your-secret-key-here-change-this-in-production"  # Change this in production!

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
app.secret_key = SECRET_KEY

# Initialize database
db = DatabaseManager(MONGO_URL)

# Global bot reference (will be set when integrated with main.py)
bot = None

def set_bot_reference(bot_instance):
    """Set the bot reference for accessing Discord data"""
    global bot
    bot = bot_instance

@app.before_request
def initialize_database():
    """Initialize database connection"""
    if not hasattr(initialize_database, 'initialized'):
        try:
            db.connect()
            logger.info("Web dashboard database connected successfully")
            initialize_database.initialized = True
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")

def require_auth(f):
    """Decorator to require authentication"""
    def decorated_function(*args, **kwargs):
        if 'license_key' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

def get_server_info(server_id: int) -> Optional[Dict[str, Any]]:
    """Get server information from Discord bot"""
    if not bot:
        return None
    
    guild = bot.get_guild(server_id)
    if not guild:
        return None
    
    return {
        'id': guild.id,
        'name': guild.name,
        'icon': guild.icon.url if guild.icon else None,
        'member_count': guild.member_count,
        'owner_id': guild.owner_id
    }

def get_user_servers(license_key: str) -> List[Dict[str, Any]]:
    """Get all servers associated with a license key"""
    # Get license key info
    key_info = db.get_license_key_by_key(license_key)
    if not key_info or not key_info.get('redeemed'):
        return []
    
    # Get server info if key is redeemed to a server
    servers = []
    if key_info.get('server_id'):
        server_info = get_server_info(key_info['server_id'])
        if server_info:
            servers.append(server_info)
    
    return servers

@app.route('/')
def index():
    """Home page - redirect to dashboard if authenticated, otherwise show login"""
    if 'license_key' in session:
        return redirect(url_for('dashboard'))
    return render_template('index.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    """Login page"""
    if request.method == 'POST':
        license_key = request.form.get('license_key', '').strip()
        
        if not license_key:
            flash('Please enter a license key', 'error')
            return render_template('login.html')
        
        # Validate license key
        key_info = db.get_license_key_by_key(license_key)
        if not key_info:
            flash('Invalid license key', 'error')
            return render_template('login.html')
        
        if not key_info.get('redeemed'):
            flash('License key has not been redeemed yet. Please use /redeem-key command in your Discord server first.', 'warning')
            return render_template('login.html')
        
        if key_info.get('disabled'):
            flash('License key has been disabled', 'error')
            return render_template('login.html')
        
        # Store license key in session
        session['license_key'] = license_key
        session['user_id'] = key_info['redeemed_by']
        session['server_id'] = key_info.get('server_id')
        
        flash('Successfully logged in!', 'success')
        return redirect(url_for('dashboard'))
    
    return render_template('login.html')

@app.route('/logout')
def logout():
    """Logout and clear session"""
    session.clear()
    flash('Successfully logged out', 'success')
    return redirect(url_for('index'))

@app.route('/dashboard')
@require_auth
def dashboard():
    """Main dashboard page"""
    license_key = session['license_key']
    server_id = session.get('server_id')
    
    if not server_id:
        flash('No server associated with this license key', 'error')
        return redirect(url_for('logout'))
    
    # Get server information
    server_info = get_server_info(server_id)
    if not server_info:
        flash('Server not found or bot is not in the server', 'error')
        return redirect(url_for('logout'))
    
    # Get server configuration
    config = db.get_server_config(server_id)
    vent_settings = db.get_vent_settings(server_id)
    tempvoice_settings = db.get_tempvoice_settings(server_id)
    gender_verification_settings = db.get_gender_verification_settings(server_id)
    dm_support_settings = db.get_dm_support_settings(server_id)
    sticky_messages = db.get_all_sticky_messages(server_id)
    
    # Check if server is fully configured
    is_configured, missing_fields = db.is_server_configured(server_id)
    
    return render_template('dashboard.html',
                         server_info=server_info,
                         config=config,
                         vent_settings=vent_settings,
                         tempvoice_settings=tempvoice_settings,
                         gender_verification_settings=gender_verification_settings,
                         dm_support_settings=dm_support_settings,
                         sticky_messages=sticky_messages,
                         is_configured=is_configured,
                         missing_fields=missing_fields)

@app.route('/configure/repping', methods=['GET', 'POST'])
@require_auth
def configure_repping():
    """Configure repping system"""
    server_id = session.get('server_id')

    if request.method == 'POST':
        trigger_word = request.form.get('trigger_word', '').strip()
        role_id = request.form.get('role_id', '').strip()
        channel_id = request.form.get('channel_id', '').strip()

        if not all([trigger_word, role_id, channel_id]):
            flash('All fields are required', 'error')
            return redirect(url_for('configure_repping'))

        try:
            role_id = int(role_id)
            channel_id = int(channel_id)
        except ValueError:
            flash('Invalid role ID or channel ID', 'error')
            return redirect(url_for('configure_repping'))

        # Save configuration
        config_update = {
            'trigger_word': trigger_word,
            'role_id': role_id,
            'channel_id': channel_id
        }

        success = db.save_server_config(server_id, config_update)

        if success:
            flash('Repping system configured successfully!', 'success')
        else:
            flash('Failed to save configuration', 'error')

        return redirect(url_for('dashboard'))

    # GET request - show form
    config = db.get_server_config(server_id)
    server_info = get_server_info(server_id)

    return render_template('configure_repping.html',
                         config=config,
                         server_info=server_info)

@app.route('/configure/vent', methods=['GET', 'POST'])
@require_auth
def configure_vent():
    """Configure vent system"""
    server_id = session.get('server_id')

    if request.method == 'POST':
        vent_channel_id = request.form.get('vent_channel_id', '').strip()

        if not vent_channel_id:
            flash('Vent channel ID is required', 'error')
            return redirect(url_for('configure_vent'))

        try:
            vent_channel_id = int(vent_channel_id)
        except ValueError:
            flash('Invalid channel ID', 'error')
            return redirect(url_for('configure_vent'))

        # Check if log channel is configured
        config = db.get_server_config(server_id)
        if not config or not config.get('log_channel_id'):
            flash('Log channel must be configured first. Please set it in the repping system configuration.', 'error')
            return redirect(url_for('configure_vent'))

        # Save vent settings
        success = db.set_vent_settings(server_id, vent_channel_id)

        if success:
            flash('Vent system configured successfully!', 'success')
        else:
            flash('Failed to save vent configuration', 'error')

        return redirect(url_for('dashboard'))

    # GET request - show form
    vent_settings = db.get_vent_settings(server_id)
    config = db.get_server_config(server_id)
    server_info = get_server_info(server_id)

    return render_template('configure_vent.html',
                         vent_settings=vent_settings,
                         config=config,
                         server_info=server_info)

@app.route('/configure/tempvoice', methods=['GET', 'POST'])
@require_auth
def configure_tempvoice():
    """Configure temp voice system"""
    server_id = session.get('server_id')

    if request.method == 'POST':
        interface_channel_id = request.form.get('interface_channel_id', '').strip()
        creator_channel_id = request.form.get('creator_channel_id', '').strip()
        default_user_limit = request.form.get('default_user_limit', '').strip()

        if not all([interface_channel_id, creator_channel_id]):
            flash('Interface and creator channel IDs are required', 'error')
            return redirect(url_for('configure_tempvoice'))

        try:
            interface_channel_id = int(interface_channel_id)
            creator_channel_id = int(creator_channel_id)
            default_user_limit = int(default_user_limit) if default_user_limit else None
        except ValueError:
            flash('Invalid channel ID or user limit', 'error')
            return redirect(url_for('configure_tempvoice'))

        # Save tempvoice settings
        success = db.set_tempvoice_settings(server_id, interface_channel_id, creator_channel_id, default_user_limit)

        if success:
            flash('Temp voice system configured successfully!', 'success')
        else:
            flash('Failed to save temp voice configuration', 'error')

        return redirect(url_for('dashboard'))

    # GET request - show form
    tempvoice_settings = db.get_tempvoice_settings(server_id)
    server_info = get_server_info(server_id)

    return render_template('configure_tempvoice.html',
                         tempvoice_settings=tempvoice_settings,
                         server_info=server_info)

@app.route('/settings', methods=['GET', 'POST'])
@require_auth
def settings():
    """Server settings page including ignored users and other configurations"""
    server_id = session.get('server_id')

    if request.method == 'POST':
        action = request.form.get('action')

        if action == 'add_ignored_user':
            user_id = request.form.get('user_id', '').strip()

            if not user_id:
                flash('User ID is required', 'error')
                return redirect(url_for('settings'))

            try:
                user_id = int(user_id)
            except ValueError:
                flash('Invalid user ID', 'error')
                return redirect(url_for('settings'))

            success = db.add_ignored_user(server_id, user_id)
            if success:
                flash('User added to ignored list successfully!', 'success')
                # Log the action
                db.log_bot_activity(
                    server_id,
                    session['user_id'],
                    "Web Dashboard User",
                    "User added to ignored list",
                    f"User ID: {user_id}",
                    "config"
                )
            else:
                flash('Failed to add user to ignored list', 'error')

        elif action == 'remove_ignored_user':
            user_id = request.form.get('user_id', '').strip()

            try:
                user_id = int(user_id)
            except ValueError:
                flash('Invalid user ID', 'error')
                return redirect(url_for('settings'))

            success = db.remove_ignored_user(server_id, user_id)
            if success:
                flash('User removed from ignored list successfully!', 'success')
                # Log the action
                db.log_bot_activity(
                    server_id,
                    session['user_id'],
                    "Web Dashboard User",
                    "User removed from ignored list",
                    f"User ID: {user_id}",
                    "config"
                )
            else:
                flash('Failed to remove user from ignored list', 'error')

        elif action == 'set_log_channel':
            log_channel_id = request.form.get('log_channel_id', '').strip()

            if not log_channel_id:
                flash('Log channel is required', 'error')
                return redirect(url_for('settings'))

            try:
                log_channel_id = int(log_channel_id)
            except ValueError:
                flash('Invalid channel ID', 'error')
                return redirect(url_for('settings'))

            # Update server config with log channel
            config_update = {'log_channel_id': log_channel_id}
            success = db.save_server_config(server_id, config_update)

            if success:
                flash('Log channel updated successfully!', 'success')
                # Log the action
                db.log_bot_activity(
                    server_id,
                    session['user_id'],
                    "Web Dashboard User",
                    "Log channel configured",
                    f"Channel ID: {log_channel_id}",
                    "config",
                    log_channel_id
                )
            else:
                flash('Failed to update log channel', 'error')

        return redirect(url_for('settings'))

    # GET request - show settings page
    config = db.get_server_config(server_id)
    server_info = get_server_info(server_id)
    ignored_users = config.get('ignored_users', []) if config else []

    return render_template('settings.html',
                         config=config,
                         ignored_users=ignored_users,
                         server_info=server_info)

@app.route('/configure/dm-support', methods=['GET', 'POST'])
@require_auth
def configure_dm_support():
    """Configure DM support system"""
    server_id = session.get('server_id')

    if request.method == 'POST':
        category_id = request.form.get('category_id', '').strip()
        support_role_id = request.form.get('support_role_id', '').strip()

        if not all([category_id, support_role_id]):
            flash('All fields are required', 'error')
            return redirect(url_for('configure_dm_support'))

        try:
            category_id = int(category_id)
            support_role_id = int(support_role_id)
        except ValueError:
            flash('Invalid category ID or role ID', 'error')
            return redirect(url_for('configure_dm_support'))

        # Check if log channel is configured
        config = db.get_server_config(server_id)
        if not config or not config.get('log_channel_id'):
            flash('Log channel must be configured first. Please set it in Settings.', 'error')
            return redirect(url_for('configure_dm_support'))

        # Create support-logs channel name
        support_logs_channel_id = None  # Will be created by the bot if needed

        # Save DM support settings
        success = db.set_dm_support_settings(server_id, category_id, support_role_id, support_logs_channel_id)

        if success:
            flash('DM support system configured successfully!', 'success')
            # Log the action
            db.log_bot_activity(
                server_id,
                session['user_id'],
                "Web Dashboard User",
                "DM support system configured",
                f"Category ID: {category_id}, Support Role ID: {support_role_id}",
                "config"
            )
        else:
            flash('Failed to save DM support configuration', 'error')

        return redirect(url_for('dashboard'))

    # GET request - show form
    dm_support_settings = db.get_dm_support_settings(server_id)
    config = db.get_server_config(server_id)
    server_info = get_server_info(server_id)

    return render_template('configure_dm_support.html',
                         dm_support_settings=dm_support_settings,
                         config=config,
                         server_info=server_info)

@app.route('/configure/gender-verification', methods=['GET', 'POST'])
@require_auth
def configure_gender_verification():
    """Configure gender verification system"""
    server_id = session.get('server_id')

    if request.method == 'POST':
        channel_id = request.form.get('channel_id', '').strip()
        category_id = request.form.get('category_id', '').strip()
        support_role_id = request.form.get('support_role_id', '').strip()
        paper_text = request.form.get('paper_text', '').strip()

        if not all([channel_id, category_id, support_role_id, paper_text]):
            flash('All fields are required', 'error')
            return redirect(url_for('configure_gender_verification'))

        try:
            channel_id = int(channel_id)
            category_id = int(category_id)
            support_role_id = int(support_role_id)
        except ValueError:
            flash('Invalid channel ID, category ID, or role ID', 'error')
            return redirect(url_for('configure_gender_verification'))

        # Save gender verification settings
        success = db.set_gender_verification_settings(server_id, channel_id, category_id, support_role_id, paper_text)

        if success:
            flash('Gender verification system configured successfully!', 'success')
            # Log the action
            db.log_bot_activity(
                server_id,
                session['user_id'],
                "Web Dashboard User",
                "Gender verification system configured",
                f"Channel ID: {channel_id}, Category ID: {category_id}, Support Role ID: {support_role_id}",
                "config"
            )
        else:
            flash('Failed to save gender verification configuration', 'error')

        return redirect(url_for('dashboard'))

    # GET request - show form
    gender_verification_settings = db.get_gender_verification_settings(server_id)
    server_info = get_server_info(server_id)

    return render_template('configure_gender_verification.html',
                         gender_verification_settings=gender_verification_settings,
                         server_info=server_info)

@app.route('/configure/sticky-messages', methods=['GET', 'POST'])
@require_auth
def configure_sticky_messages():
    """Configure sticky messages"""
    server_id = session.get('server_id')

    if request.method == 'POST':
        action = request.form.get('action')

        if action == 'create':
            channel_id = request.form.get('channel_id', '').strip()
            content = request.form.get('content', '').strip()

            if not all([channel_id, content]):
                flash('Channel and content are required', 'error')
                return redirect(url_for('configure_sticky_messages'))

            try:
                channel_id = int(channel_id)
            except ValueError:
                flash('Invalid channel ID', 'error')
                return redirect(url_for('configure_sticky_messages'))

            # Create sticky message
            success = db.create_sticky_message(server_id, channel_id, content)

            if success:
                flash('Sticky message created successfully!', 'success')
                # Log the action
                db.log_bot_activity(
                    server_id,
                    session['user_id'],
                    "Web Dashboard User",
                    "Sticky message created",
                    f"Channel ID: {channel_id}, Content length: {len(content)} characters",
                    "sticky",
                    channel_id
                )
            else:
                flash('Failed to create sticky message', 'error')

        elif action == 'remove':
            channel_id = request.form.get('channel_id', '').strip()

            try:
                channel_id = int(channel_id)
            except ValueError:
                flash('Invalid channel ID', 'error')
                return redirect(url_for('configure_sticky_messages'))

            # Remove sticky message
            success = db.remove_sticky_message(server_id, channel_id)

            if success:
                flash('Sticky message removed successfully!', 'success')
                # Log the action
                db.log_bot_activity(
                    server_id,
                    session['user_id'],
                    "Web Dashboard User",
                    "Sticky message removed",
                    f"Channel ID: {channel_id}",
                    "sticky"
                )
            else:
                flash('Failed to remove sticky message', 'error')

        return redirect(url_for('configure_sticky_messages'))

    # GET request - show form
    sticky_messages = db.get_all_sticky_messages(server_id)
    server_info = get_server_info(server_id)

    return render_template('configure_sticky_messages.html',
                         sticky_messages=sticky_messages,
                         server_info=server_info)

# Keep the old automod route for backward compatibility
@app.route('/configure/automod')
@require_auth
def configure_automod():
    """Redirect to settings page"""
    return redirect(url_for('settings'))

@app.route('/api/server-info')
@require_auth
def api_server_info():
    """API endpoint to get server information"""
    server_id = session.get('server_id')
    server_info = get_server_info(server_id)

    if not server_info:
        return jsonify({'error': 'Server not found'}), 404

    return jsonify(server_info)

@app.route('/api/channels')
@require_auth
def api_channels():
    """API endpoint to get server channels"""
    server_id = session.get('server_id')

    if not bot:
        return jsonify({'error': 'Bot not available'}), 503

    guild = bot.get_guild(server_id)
    if not guild:
        return jsonify({'error': 'Server not found'}), 404

    channels = []
    for channel in guild.channels:
        # Check channel type by class name to avoid importing discord
        channel_type_name = type(channel).__name__

        if channel_type_name == 'TextChannel':
            channels.append({
                'id': str(channel.id),
                'name': channel.name,
                'type': 'text',
                'category': channel.category.name if channel.category else None
            })
        elif channel_type_name == 'VoiceChannel':
            channels.append({
                'id': str(channel.id),
                'name': channel.name,
                'type': 'voice',
                'category': channel.category.name if channel.category else None
            })
        elif channel_type_name == 'CategoryChannel':
            channels.append({
                'id': str(channel.id),
                'name': channel.name,
                'type': 'category',
                'category': None
            })

    return jsonify(channels)

@app.route('/api/roles')
@require_auth
def api_roles():
    """API endpoint to get server roles (only roles below bot's highest role)"""
    server_id = session.get('server_id')

    if not bot:
        return jsonify({'error': 'Bot not available'}), 503

    guild = bot.get_guild(server_id)
    if not guild:
        return jsonify({'error': 'Server not found'}), 404

    # Get bot's highest role position
    bot_member = guild.get_member(bot.user.id)
    if not bot_member:
        return jsonify({'error': 'Bot not found in server'}), 404

    bot_highest_position = bot_member.top_role.position

    roles = []
    for role in guild.roles:
        # Only include roles that are:
        # 1. Not @everyone
        # 2. Below the bot's highest role position
        # 3. Not managed by integration (like bot roles)
        if (role.name != '@everyone' and
            role.position < bot_highest_position and
            not role.managed):
            roles.append({
                'id': str(role.id),
                'name': role.name,
                'color': str(role.color),
                'position': role.position,
                'mentionable': role.mentionable
            })

    # Sort by position (highest first)
    roles.sort(key=lambda x: x['position'], reverse=True)

    return jsonify(roles)

@app.route('/logs')
@require_auth
def logs():
    """Dashboard logs viewer"""
    server_id = session.get('server_id')

    # Get filter parameters
    category = request.args.get('category', '')
    limit = min(int(request.args.get('limit', 100)), 500)  # Max 500 logs

    # Get logs from database
    logs = db.get_bot_logs(server_id, limit=limit, category=category if category else None)

    # Get live statistics for activity breakdown
    stats = db.get_log_statistics(server_id, days=0)  # Use 0 to indicate all-time stats

    # Get server info
    server_info = get_server_info(server_id)

    return render_template('logs.html',
                         logs=logs,
                         stats=stats,
                         server_info=server_info,
                         current_category=category,
                         current_limit=limit)

@app.route('/api/logs')
@require_auth
def api_logs():
    """API endpoint for logs (for AJAX updates)"""
    server_id = session.get('server_id')

    category = request.args.get('category', '')
    limit = min(int(request.args.get('limit', 100)), 500)

    logs = db.get_bot_logs(server_id, limit=limit, category=category if category else None)

    # Convert datetime objects to strings for JSON serialization
    for log in logs:
        if 'timestamp' in log:
            log['timestamp'] = log['timestamp'].isoformat()

    return jsonify(logs)

@app.route('/stats')
@require_auth
def stats():
    """Dashboard stats viewer"""
    server_id = session.get('server_id')

    # Get server statistics (with live Discord data if bot is available)
    server_stats = db.get_server_statistics(server_id, bot)

    # Get live log statistics for activity breakdown
    log_stats = db.get_log_statistics(server_id, days=0)  # Use 0 to indicate all-time stats

    # Get current repping users (with live Discord data if bot is available)
    current_repping_users = db.get_current_repping_users(server_id, bot)

    # Get active temp voice channels with user info
    active_temp_channels = db.get_active_temp_channels_with_users(server_id)

    # Get server info
    server_info = get_server_info(server_id)

    return render_template('stats.html',
                         server_stats=server_stats,
                         log_stats=log_stats,
                         server_info=server_info,
                         current_repping_users=current_repping_users,
                         active_temp_channels=active_temp_channels)

@app.route('/api/cleanup-logs', methods=['POST'])
@require_auth
def api_cleanup_logs():
    """API endpoint to manually clean up old logs"""
    server_id = session.get('server_id')

    try:
        deleted_count = db.cleanup_all_server_logs(server_id, max_logs=100)

        # Log the cleanup action
        db.log_bot_activity(
            server_id,
            session['user_id'],
            "Web Dashboard User",
            "Manual log cleanup performed",
            f"Deleted {deleted_count} old log entries",
            "config"
        )

        return jsonify({
            'success': True,
            'deleted_count': deleted_count,
            'message': f'Successfully cleaned up {deleted_count} old log entries'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/giveaways')
@require_auth
def giveaways():
    """Giveaways management page"""
    server_id = session.get('server_id')

    # Get server info
    server_info = get_server_info(server_id)

    # Get server giveaways
    server_giveaways = db.get_server_giveaways(server_id)

    # Get channels for dropdown
    channels = []
    if bot:
        guild = bot.get_guild(server_id)
        if guild:
            channels = [
                {'id': channel.id, 'name': channel.name}
                for channel in guild.text_channels
                if channel.permissions_for(guild.me).send_messages
            ]

    return render_template('giveaways.html',
                         server_info=server_info,
                         giveaways=server_giveaways,
                         channels=channels)

@app.route('/create_giveaway', methods=['POST'])
@require_auth
def create_giveaway():
    """Create a new giveaway"""
    server_id = session.get('server_id')

    try:
        # Get form data
        channel_id = int(request.form.get('channel_id'))
        host_user_id = int(request.form.get('host_user_id'))
        item = request.form.get('item', '').strip()
        requirements = request.form.get('requirements', '').strip()
        winners = int(request.form.get('winners', 1))
        duration_days = int(request.form.get('duration_days', 0))
        duration_hours = int(request.form.get('duration_hours', 0))
        duration_minutes = int(request.form.get('duration_minutes', 0))

        # Validation
        if not all([channel_id, host_user_id, item, requirements]):
            flash('All fields are required.', 'error')
            return redirect(url_for('giveaways'))

        if winners < 1 or winners > 50:
            flash('Winners must be between 1 and 50.', 'error')
            return redirect(url_for('giveaways'))

        if duration_days == 0 and duration_hours == 0 and duration_minutes == 0:
            flash('Duration must be at least 1 minute.', 'error')
            return redirect(url_for('giveaways'))

        # Calculate end time
        from datetime import timedelta
        total_minutes = (duration_days * 24 * 60) + (duration_hours * 60) + duration_minutes
        end_time = datetime.now(timezone.utc) + timedelta(minutes=total_minutes)

        # Create giveaway in database
        giveaway_id = db.create_giveaway(
            server_id=server_id,
            channel_id=channel_id,
            host_user_id=host_user_id,
            item=item,
            requirements=requirements,
            winners=winners,
            end_time=end_time
        )

        # Send giveaway message to Discord
        success = False
        if bot:
            try:
                # Use the bot's event loop to send the message
                import asyncio

                async def create_and_send_giveaway():
                    guild = bot.get_guild(server_id)
                    if not guild:
                        return False

                    channel = guild.get_channel(channel_id)
                    if not channel:
                        return False

                    # Create giveaway embed
                    import discord
                    embed = discord.Embed(
                        title=item,
                        description=requirements,
                        color=discord.Color.gold()
                    )

                    # Calculate time remaining
                    current_time = datetime.now(timezone.utc)
                    time_diff = end_time - current_time
                    days = time_diff.days
                    hours, remainder = divmod(time_diff.seconds, 3600)
                    minutes, _ = divmod(remainder, 60)

                    if days > 0:
                        time_str = f"{days} days, {hours} hours"
                    elif hours > 0:
                        time_str = f"{hours} hours, {minutes} minutes"
                    else:
                        time_str = f"{minutes} minutes"

                    embed.add_field(name="⏰ Ends", value=f"In {time_str}", inline=True)
                    embed.add_field(name="👤 Hosted by", value=f"<@{host_user_id}>", inline=True)
                    embed.add_field(name="📊 Entries", value="0", inline=True)
                    embed.add_field(name="🏆 Winners", value=str(winners), inline=True)

                    # Format end time for footer
                    end_time_str = end_time.strftime("%B %d, %Y at %I:%M %p UTC")
                    embed.set_footer(text=f"{end_time_str} | .gg/leakin")

                    # Create view with button (import from main.py)
                    from main import GiveawayView
                    view = GiveawayView(giveaway_id)

                    # Send the message
                    message = await channel.send(embed=embed, view=view)

                    # Update database with message ID
                    db.update_giveaway_message_id(giveaway_id, message.id)

                    return True

                # Schedule the coroutine to run in the bot's event loop
                future = asyncio.run_coroutine_threadsafe(create_and_send_giveaway(), bot.loop)
                success = future.result(timeout=10)  # 10 second timeout

            except Exception as e:
                logger.error(f"Error sending giveaway to Discord: {e}")
                success = False

        if success:
            flash('Giveaway created successfully!', 'success')
            # Log the action
            db.log_bot_activity(
                server_id,
                session['user_id'],
                "Web Dashboard User",
                "Giveaway created",
                f"Item: {item}, Winners: {winners}, Channel: {channel_id}",
                "giveaway",
                channel_id
            )
        else:
            flash('Giveaway created but failed to send message to Discord.', 'warning')

    except ValueError as e:
        flash('Invalid input values.', 'error')
    except Exception as e:
        logger.error(f"Error creating giveaway: {e}")
        flash('An error occurred while creating the giveaway.', 'error')

    return redirect(url_for('giveaways'))

if __name__ == '__main__':
    # For development only
    app.run(debug=True, host='0.0.0.0', port=5000)
